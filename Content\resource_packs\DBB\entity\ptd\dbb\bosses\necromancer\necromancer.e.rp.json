{
  "format_version": "1.10.0",
  "minecraft:client_entity": {
    "description": {
      "identifier": "ptd_dbb:necromancer",
      "min_engine_version": "1.16.0",
      "materials": {
        "default": "entity_emissive_alpha",
        "book_aura": "emissive_translucent"
      },
      "textures": {
        "default": "textures/ptd/dbb/entity/bosses/necromancer/default"
      },
      "geometry": {
        "default": "geometry.ptd_dbb_necromancer"
      },
      "animations": {
        "look_at_target": "animation.common.look_at_target",
        "no_effects": "animation.ptd_dbb_necromancer.no_effects",
        "no_effects_except_portal": "animation.ptd_dbb_necromancer.no_effects_except_portal",
        "no_effects_except_orb": "animation.ptd_dbb_necromancer.no_effects_except_orb",
        "no_effects_except_shadows": "animation.ptd_dbb_necromancer.no_effects_except_shadows",
        "spawn": "animation.ptd_dbb_necromancer.spawn",
        "idle": "animation.ptd_dbb_necromancer.idle",
        "move": "animation.ptd_dbb_necromancer.move",
        "death": "animation.ptd_dbb_necromancer.death",
        "cataclysm": "animation.ptd_dbb_necromancer.cataclysm",
        "book_of_the_damned": "animation.ptd_dbb_necromancer.book_of_the_damned",
        "soul_drain": "animation.ptd_dbb_necromancer.soul_drain",
        "phantom_phase_start": "animation.ptd_dbb_necromancer.phantom_phase_start",
        "phantom_phase_end": "animation.ptd_dbb_necromancer.phantom_phase_end",
        "undead_summon": "animation.ptd_dbb_necromancer.undead_summon",
        "arcane_blast": "animation.ptd_dbb_necromancer.arcane_blast",
        "soul_hands": "animation.ptd_dbb_necromancer.soul_hands",
        "soul_trap": "animation.ptd_dbb_necromancer.soul_trap",
        "general": "controller.animation.ptd_dbb_necromancer.general"
      },
      "scripts": {
        "should_update_bones_and_effects_offscreen": true,
        "animate": [
          "general",
          {
            "look_at_target": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && q.property('ptd_dbb:attack') != 'phantom_phase_start' && q.property('ptd_dbb:attack') != 'phantom_phase_end'"
          },
          // Disable all effects including the orb when not using a "cataclysm" attack or phantom phase
          {
            "no_effects": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && q.property('ptd_dbb:attack') != 'cataclysm' && q.property('ptd_dbb:attack') != 'phantom_phase_start' && q.property('ptd_dbb:attack') != 'phantom_phase_end'"
          },
          // Disable all effects except the orb when using a "cataclysm" attack
          {
            "no_effects_except_orb": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && (q.property('ptd_dbb:attack') == 'cataclysm')"
          },
          // Disable all effects except the portal when spawning
          {
            "no_effects_except_portal": "q.property('ptd_dbb:spawning') == true"
          },
          // Disable all effects except the shadows when using a "phantom_phase_start" and "phantom_phase_end" attack
          {
            "no_effects_except_shadows": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && (q.property('ptd_dbb:attack') == 'phantom_phase_start' || q.property('ptd_dbb:attack') == 'phantom_phase_end')"
          }
        ]
      },
      "render_controllers": ["controller.render.ptd_dbb_necromancer"],
      "sound_effects": {
        "spawn": "mob.ptd_dbb_necromancer.spawn",
        "cataclysm": "mob.ptd_dbb_necromancer.cataclysm",
        "book_of_the_damned": "mob.ptd_dbb_necromancer.cataclysm",
        "soul_drain": "mob.ptd_dbb_necromancer.soul_drain",
        "phantom_phase_start": "mob.ptd_dbb_necromancer.phantom_phase_start",
        "phantom_phase_end": "mob.ptd_dbb_necromancer.phantom_phase_end",
        "arcane_blast": "mob.ptd_dbb_necromancer.arcane_blast",
        "soul_hands": "mob.ptd_dbb_necromancer.soul_hands",
        "soul_trap": "mob.ptd_dbb_necromancer.soul_hands",
        "undead_summon": "mob.ptd_dbb_necromancer.undead_summon"
      },
      "spawn_egg": {
        "base_color": "#cfb43a",
        "overlay_color": "#26bccd"
      }
    }
  }
}
