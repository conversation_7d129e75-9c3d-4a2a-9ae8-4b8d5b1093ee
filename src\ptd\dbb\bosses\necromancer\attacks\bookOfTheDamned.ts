import { Entity, EntityComponentTypes, system, Vector3, GameMode } from "@minecraft/server";
import { getDirection } from "../../../utilities/vector3";
import { faceDirection } from "../../../entities/projectileRotation";

/**
 * Attack timing constants for book of the damned attack phases
 */
const DAMAGE_START_TIMING = 70; // Start firing skulls at tick 70 (3.5 seconds)
const DAMAGE_END_TIMING = 150; // End firing skulls at tick 150 (7.5 seconds)

/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 200; // 10 seconds (same as cataclysm)

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;

/**
 * Configuration for the book of the damned attack
 */
const BOOK_OF_THE_DAMNED_CONFIG = {
  /** Projectile entity type */
  PROJECTILE_TYPE: "ptd_dbb:flying_skull",
  /** Projectile speed (velocity multiplier) */
  PROJECTILE_SPEED: 0.3,
  /** Tick interval for applying impulse */
  IMPULSE_INTERVAL: 1,
  /** Vertical offset from necromancer position */
  VERTICAL_OFFSET: 1.5,
  /** Delay between firing skulls in ticks */
  SKULL_FIRE_DELAY: 5 // Fire every 5 ticks during damage phase
};

/**
 * Gets the target for Book of the Damned attack based on specific targeting rules:
 * 1. If players in survival/adventure mode exist, target them randomly
 * 2. If no players, target bosses and minions randomly
 *
 * @param necromancer The necromancer entity
 * @param searchDistance Maximum distance to search for targets
 * @returns The selected target entity or undefined if no valid target
 */
function getBookOfTheDamnedTarget(necromancer: Entity, searchDistance: number = 32): Entity | undefined {
  try {
    const location = necromancer.location;

    // Get all valid players in survival/adventure mode
    const validPlayers = necromancer.dimension.getPlayers({
      location: location,
      maxDistance: searchDistance,
      excludeGameModes: [GameMode.creative, GameMode.spectator]
    });

    // If players exist, randomly select one
    if (validPlayers.length > 0) {
      const randomIndex = Math.floor(Math.random() * validPlayers.length);
      return validPlayers[randomIndex];
    }

    // No players found - target bosses and minions randomly
    const targetBosses = necromancer.dimension.getEntities({
      location: location,
      maxDistance: searchDistance,
      families: ["boss"],
      excludeFamilies: ["necromancer"]
    });

    const targetMinions = necromancer.dimension.getEntities({
      location: location,
      maxDistance: searchDistance,
      families: ["minion"],
      excludeFamilies: ["necromancer"]
    });

    // Filter out spawning or dead bosses and minions
    const validBosses = targetBosses.filter(boss => {
      const isSpawning = boss.getProperty("ptd_dbb:spawning") as boolean;
      const isDead = boss.getProperty("ptd_dbb:dead") as boolean;
      return !isSpawning && !isDead;
    });
    const validMinions = targetMinions.filter(minion => {
      const isSpawning = minion.getProperty("ptd_dbb:spawning") as boolean;
      const isDead = minion.getProperty("ptd_dbb:dead") as boolean;
      return !isSpawning && !isDead;
    });

    // Combine all valid non-player targets (minions don't need filtering like bosses)
    const allTargets = [...validBosses, ...validMinions];

    if (allTargets.length > 0) {
      // Randomly select from available targets
      const randomIndex = Math.floor(Math.random() * allTargets.length);
      return allTargets[randomIndex];
    }
    return undefined;
  } catch (error) {
    console.warn(`Error in getBookOfTheDamnedTarget: ${error}`);
    return undefined;
  }
}

/**
 * Executes the book of the damned attack for the Necromancer using the new timing system
 * Uses localized runTimeout for skull firing phase, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 */
export function executeBookOfTheDamnedAttack(necromancer: Entity): void {
  // Start skull firing phase at tick 70
  let skullFiringInterval: number;

  let damageStartTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(damageStartTiming);
        return;
      }

      if (necromancer.getProperty("ptd_dbb:attack") === "book_of_the_damned") {
        // Start continuous skull firing phase
        skullFiringInterval = system.runInterval(() => {
          try {
            const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
            if (isDead || necromancer.getProperty("ptd_dbb:attack") !== "book_of_the_damned") {
              system.clearRun(skullFiringInterval);
              return;
            }

            fireSkull(necromancer);
          } catch (error) {
            system.clearRun(skullFiringInterval);
          }
        }, BOOK_OF_THE_DAMNED_CONFIG.SKULL_FIRE_DELAY);

        // Stop skull firing at the end of damage phase
        system.runTimeout(() => {
          if (skullFiringInterval) {
            system.clearRun(skullFiringInterval);
          }
        }, DAMAGE_END_TIMING - DAMAGE_START_TIMING);
      }
    } catch (error) {
      system.clearRun(damageStartTiming);
    }
  }, DAMAGE_START_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        if (skullFiringInterval) system.clearRun(skullFiringInterval);
        return;
      }

      if (necromancer.getProperty("ptd_dbb:attack") === "book_of_the_damned") {
        necromancer.triggerEvent("ptd_dbb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = necromancer.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      necromancer.setProperty("ptd_dbb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Fires a single skull projectile at the target using the new targeting system
 * @param necromancer The necromancer entity
 */
function fireSkull(necromancer: Entity): void {
  try {
    // Find the target using the new Book of the Damned targeting logic
    const target = getBookOfTheDamnedTarget(necromancer, 32);
    if (!target) return;

    const headLoc = necromancer.getHeadLocation();
    const viewDir = necromancer.getViewDirection();

    // Spawn the flying skull projectile at the location of the necromancer's book
    const spawnPos: Vector3 = {
      x: headLoc.x + viewDir.x * 2.3,
      y: headLoc.y + BOOK_OF_THE_DAMNED_CONFIG.VERTICAL_OFFSET,
      z: headLoc.z + viewDir.z * 2.3
    };

    // Get the target location with a y-offset
    const targetLoc: Vector3 = {
      x: target.getHeadLocation().x,
      y: target.getHeadLocation().y - 0.2,
      z: target.getHeadLocation().z
    };

    // Calculate direction toward the target (already normalized)
    const direction = getDirection(spawnPos, targetLoc);

    // Calculate velocity based on direction and speed
    const velocity: Vector3 = {
      x: direction.x * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED,
      y: direction.y * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED,
      z: direction.z * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED
    };

    // Spawn the flying skull entity
    const projectile = necromancer.dimension.spawnEntity(BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_TYPE, spawnPos);

    if (!projectile) return;
    
    // Tag the projectile as a book of the damned projectile
    projectile.addTag(`ptd_dbb:book_of_the_damned_projectile_${necromancer.id}`);

    // Apply initial impulse
    const projectileComponent = projectile.getComponent(EntityComponentTypes.Projectile);
    if (!projectileComponent) return;

    // Apply initial impulse
    projectileComponent.shoot(velocity);

    // Set up interval to continuously apply impulse in the SAME direction
    const intervalId = system.runInterval(() => {
      try {
        // Check if entity is still valid/exists
        if (!projectile) {
          system.clearRun(intervalId);
          return;
        }

        // Apply impulse again using the SAME velocity vector
        const projectileComponent = projectile.getComponent(EntityComponentTypes.Projectile);
        if (projectileComponent) {
          projectileComponent.shoot(velocity);
        }

        // Constantly face toward the direction
        faceDirection(projectile, spawnPos, targetLoc);
      } catch (error) {
        // If any error occurs (likely because projectile no longer exists)
        system.clearRun(intervalId);
      }
    }, BOOK_OF_THE_DAMNED_CONFIG.IMPULSE_INTERVAL);

    // Play a sound effect for each skull fired
    necromancer.dimension.playSound("mob.blaze.shoot", headLoc);
  } catch (error) {
    console.warn(`Error in book of the damned attack: ${error}`);
  }
}
