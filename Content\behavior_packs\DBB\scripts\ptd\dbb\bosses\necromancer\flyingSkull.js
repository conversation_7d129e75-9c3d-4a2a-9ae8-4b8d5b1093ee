import { EffectTypes } from "@minecraft/server";
/**
 *
 * @param skull The flying skull entity
 * @returns
 */
export function handleFlyingSkullHit(event) {
    try {
        const { projectile: skull, dimension, location, tags } = event;
        if (!skull)
            return;
        // Find the relevant tag by searching through all tags
        const arcaneBlastTag = tags.find(tag => tag.includes("ptd_dbb:arcane_blast_projectile"));
        const bookOfTheDamnedTag = tags.find(tag => tag.includes("ptd_dbb:book_of_the_damned_projectile"));
        // Get the necromancer that fired this projectile
        const necromancers = dimension.getEntities({ location, maxDistance: 64, type: "ptd_dbb:necromancer" });
        let necromancer;
        if (arcaneBlastTag) {
            necromancer = necromancers.find((necro) => arcaneBlastTag.includes(necro.id));
        }
        else if (bookOfTheDamnedTag) {
            necromancer = necromancers.find((necro) => bookOfTheDamnedTag.includes(necro.id));
        }
        // Check if the tag is for an arcane blast projectile or a book of the damned projectile
        if (arcaneBlastTag) {
            // Create explosion with wither effect
            const witherEffect = EffectTypes.get('minecraft:wither');
            if (!witherEffect) {
                // Fallback to string-based approach
                const fallbackEffect = EffectTypes.get("minecraft:wither");
                createExplosion(event, necromancer, fallbackEffect);
            }
            else {
                createExplosion(event, necromancer, witherEffect);
            }
            return;
        }
        else if (bookOfTheDamnedTag) {
            // Create explosion with no effect
            createExplosion(event, necromancer);
            return;
        }
        // If no relevant tags, explode normally
        else {
            createExplosion(event, undefined);
        }
    }
    catch (error) {
        console.warn(`Error in handleFlyingSkullHit: ${error}`);
        return;
    }
    return;
}
/**
 *
 * @param skull
 * @param effect
 * @returns
 */
function createExplosion(event, source, effect) {
    try {
        const { projectile: skull, dimension, location } = event;
        if (!skull)
            return;
        // Create a more visible explosion
        const explosionRadius = 4;
        if (source) {
            dimension.createExplosion(location, explosionRadius, {
                source: source,
                breaksBlocks: false,
                causesFire: false,
                allowUnderwater: true
            });
        }
        else {
            dimension.createExplosion(location, explosionRadius, {
                breaksBlocks: false,
                causesFire: false,
                allowUnderwater: true
            });
        }
        // Apply effect to nearby entities if provided
        if (effect) {
            const nearbyEntities = dimension.getEntities({
                location: location,
                maxDistance: 6,
                excludeTypes: ["minecraft:xp_orb", "minecraft:item", "ptd_dbb:rock", "ptd_dbb:flying_skull"],
                excludeFamilies: ["necromancer", "inanimate"]
            });
            // Apply effect to each entity
            nearbyEntities.forEach((entity) => {
                try {
                    entity.addEffect(effect, 200, { amplifier: 1, showParticles: true });
                }
                catch (effectError) {
                    console.warn(`Failed to apply effect to ${entity.typeId}: ${effectError}`);
                }
            });
        }
    }
    catch (error) {
        console.warn(`Error in createExplosion: ${error}`);
        return;
    }
    return;
}
